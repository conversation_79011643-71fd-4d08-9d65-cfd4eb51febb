#!/usr/bin/env python
# -*- coding: utf-8 -*-

from modules.excel_manager import crea_template_excel, crea_template_parco_bobine
import pandas as pd
import os

def test_template_aggiornati():
    """Test dei template aggiornati con le nuove specifiche."""
    
    print('=== TEST TEMPLATE AGGIORNATI ===')

    # Test template cavi
    print('1. Template Cavi...')
    template_cavi = crea_template_excel('test_template_cavi_aggiornato.xlsx')
    if template_cavi:
        df_cavi = pd.read_excel(template_cavi, skiprows=1)
        print(f'   ✅ Creato: {template_cavi}')
        print(f'   📋 Colonne: {list(df_cavi.columns)}')
        print(f'   🔍 Contiene "formazione": {"formazione" in df_cavi.columns}')
        print(f'   ❌ Contiene "sezione": {"sezione" in df_cavi.columns}')
        print(f'   ❌ Contiene "n_conduttori": {"n_conduttori" in df_cavi.columns}')
        print(f'   ❌ Contiene "sh": {"sh" in df_cavi.columns}')
        
        # Verifica dati di esempio
        if len(df_cavi) > 0:
            print(f'   📊 Esempio formazione: {df_cavi.iloc[0]["formazione"]}')

    print()

    # Test template parco bobine
    print('2. Template Parco Bobine...')
    template_bobine = crea_template_parco_bobine('test_template_bobine_aggiornato.xlsx')
    if template_bobine:
        df_bobine = pd.read_excel(template_bobine, skiprows=1)
        print(f'   ✅ Creato: {template_bobine}')
        print(f'   📋 Colonne: {list(df_bobine.columns)}')
        print(f'   🔍 Contiene "formazione": {"formazione" in df_bobine.columns}')
        print(f'   ❌ Contiene "sezione": {"sezione" in df_bobine.columns}')
        print(f'   ❌ Contiene "n_conduttori": {"n_conduttori" in df_bobine.columns}')
        
        # Verifica dati di esempio
        if len(df_bobine) > 0:
            print(f'   📊 Esempio formazione: {df_bobine.iloc[0]["formazione"]}')

    print()
    print('✅ AGGIORNAMENTI COMPLETATI!')
    print('📝 I template ora usano "formazione" invece di "sezione"')
    print('🗑️ I campi n_conduttori e sh sono stati rimossi (campi morti)')
    
    # Pulizia file di test
    for file_test in ['test_template_cavi_aggiornato.xlsx', 'test_template_bobine_aggiornato.xlsx']:
        try:
            if os.path.exists(file_test):
                os.remove(file_test)
                print(f'🧹 Rimosso: {file_test}')
        except:
            pass

if __name__ == "__main__":
    test_template_aggiornati()
