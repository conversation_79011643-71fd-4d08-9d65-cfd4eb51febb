<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Download Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .success {
            border-color: #4CAF50;
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            border-color: #f44336;
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <h1>Test Download Template Excel</h1>
    <p>Questo test verifica che il download automatico dei template Excel funzioni correttamente.</p>
    
    <div>
        <button class="button" onclick="testDownloadCaviTemplate()">
            Test Download Template Cavi
        </button>
        
        <button class="button" onclick="testDownloadParcoBobineTemplate()">
            Test Download Template Parco Bobine
        </button>
    </div>
    
    <div id="result" class="result" style="display: none;">
        <h3>Risultato:</h3>
        <p id="resultText"></p>
    </div>

    <script>
        // Funzione helper per scaricare automaticamente un file
        function downloadFile(url, filename) {
            try {
                // Crea un elemento <a> temporaneo per il download
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.target = '_blank';
                
                // Aggiungi l'elemento al DOM temporaneamente
                document.body.appendChild(link);
                
                // Simula il click per avviare il download
                link.click();
                
                // Rimuovi l'elemento dal DOM
                document.body.removeChild(link);
                
                console.log(`Download avviato per: ${filename}`);
                return true;
            } catch (error) {
                console.error('Errore durante il download automatico:', error);
                return false;
            }
        }

        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';
        }

        async function testDownloadCaviTemplate() {
            try {
                showResult('Creazione template cavi in corso...', true);
                
                // Simula la chiamata API (sostituisci con l'URL reale del tuo backend)
                const response = await fetch('http://localhost:8001/api/excel/template-cavi', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer test_token', // Sostituisci con un token valido
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.file_url) {
                        // Avvia il download automatico
                        const downloadSuccess = downloadFile(data.file_url, 'template_cavi.xlsx');
                        
                        if (downloadSuccess) {
                            showResult('✅ Template cavi creato con successo! Il download dovrebbe essere iniziato automaticamente. Controlla la cartella Download del tuo browser.', true);
                        } else {
                            showResult('⚠️ Template creato ma errore nel download automatico. URL: ' + data.file_url, false);
                        }
                    } else {
                        showResult('❌ Template creato ma nessun URL di download ricevuto.', false);
                    }
                } else {
                    const errorData = await response.json();
                    showResult('❌ Errore nella creazione del template: ' + (errorData.detail || 'Errore sconosciuto'), false);
                }
            } catch (error) {
                showResult('❌ Errore di rete: ' + error.message, false);
            }
        }

        async function testDownloadParcoBobineTemplate() {
            try {
                showResult('Creazione template parco bobine in corso...', true);
                
                // Simula la chiamata API (sostituisci con l'URL reale del tuo backend)
                const response = await fetch('http://localhost:8001/api/excel/template-parco-bobine', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer test_token', // Sostituisci con un token valido
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.file_url) {
                        // Avvia il download automatico
                        const downloadSuccess = downloadFile(data.file_url, 'template_parco_bobine.xlsx');
                        
                        if (downloadSuccess) {
                            showResult('✅ Template parco bobine creato con successo! Il download dovrebbe essere iniziato automaticamente. Controlla la cartella Download del tuo browser.', true);
                        } else {
                            showResult('⚠️ Template creato ma errore nel download automatico. URL: ' + data.file_url, false);
                        }
                    } else {
                        showResult('❌ Template creato ma nessun URL di download ricevuto.', false);
                    }
                } else {
                    const errorData = await response.json();
                    showResult('❌ Errore nella creazione del template: ' + (errorData.detail || 'Errore sconosciuto'), false);
                }
            } catch (error) {
                showResult('❌ Errore di rete: ' + error.message, false);
            }
        }
    </script>
</body>
</html>
