#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import pandas as pd
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).resolve().parent))

from modules.excel_manager import importa_parco_bobine_da_excel

def test_import_parco_bobine():
    """Test dell'importazione del parco bobine con i nuovi nomi dei campi."""
    
    # Crea un file Excel di test con i nomi corretti dei campi
    test_data = [
        {
            "id_bobina": "TEST1",
            "utility": "Power",
            "tipologia": "TAAGSHBHLV01D",
            "n_conduttori": "0",
            "sezione": "1X240MM2",
            "metri_totali": 500.0,
            "ubicazione_bobina": "Magazzino A",
            "fornitore": "Prysmian",
            "n_ddt": "DDT123456",
            "data_ddt": "01/01/2025"
        },
        {
            "id_bobina": "",  # Vuoto per test generazione automatica
            "utility": "Control",
            "tipologia": "TBAACHBHLE04I",
            "n_conduttori": "0",
            "sezione": "3X2,5+YG arm",
            "metri_totali": 300.0,
            "ubicazione_bobina": "Magazzino B",
            "fornitore": "Nexans",
            "n_ddt": "DDT654321",
            "data_ddt": "15/01/2025"
        }
    ]
    
    # Crea il DataFrame e salva in Excel
    df = pd.DataFrame(test_data)
    test_file = "test_parco_bobine_import.xlsx"
    df.to_excel(test_file, index=False)
    
    print(f"File di test creato: {test_file}")
    print("Colonne nel file:")
    for col in df.columns:
        print(f"  - {col}")
    
    # Test dell'importazione
    print("\n=== TEST IMPORTAZIONE ===")
    try:
        # Usa cantiere ID 1 per il test
        success, message, count = importa_parco_bobine_da_excel(
            test_file, 
            id_cantiere=1, 
            config_choice="2",  # Codici personalizzati
            auto_choice="2"     # Generazione automatica per campi vuoti
        )
        
        print(f"Successo: {success}")
        print(f"Messaggio: {message}")
        print(f"Bobine importate: {count}")
        
    except Exception as e:
        print(f"Errore durante l'importazione: {e}")
        import traceback
        traceback.print_exc()
    
    # Pulisci il file di test
    try:
        os.remove(test_file)
        print(f"\nFile di test rimosso: {test_file}")
    except:
        pass

if __name__ == "__main__":
    test_import_parco_bobine()
