#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import random
from datetime import datetime
import os

def genera_test_100_cavi():
    """
    Genera un file Excel di test con 100 cavi realistici per il sistema.
    """

    # Dati realistici per la generazione
    sistemi = ["HVAC", "BMS", "Lighting", "Power", "Security", "Fire Safety", "Data", "Audio/Video"]

    utilities = ["Power", "Control", "Data", "Signal", "Emergency", "Lighting"]

    colori_cavo = ["Nero", "Bianco", "Rosso", "Blu", "Verde", "<PERSON><PERSON><PERSON>", "Grigio", "Marrone"]

    tipologie = [
        "FG16OR16", "FROR", "N07V-K", "H07RN-F", "LIYCY", "LIYY",
        "UTP Cat6", "FTP Cat6A", "RG58", "RG6", "TAAGSHBHLV01D",
        "TACHCHBHLU01I", "TBAACHBHLE04I", "FG7OR", "FG7R", "N1VV-K"
    ]

    sezioni = [
        "1x1.5", "1x2.5", "1x4", "1x6", "1x10", "1x16", "1x25", "1x35", "1x50",
        "2x1.5", "2x2.5", "2x4", "3x1.5", "3x2.5", "3x4", "4x1.5", "4x2.5",
        "3x2.5+2.5YG", "4x1.5+SH", "5x1.5", "7x1.5", "12x1.5", "19x1.5",
        "1X240MM2", "1x185+SH", "3X2,5+YG arm", "4x0.75+SH", "2x0.5+SH"
    ]

    ubicazioni_partenza = [
        "Quadro Principale", "Quadro Secondario", "Quadro Controllo", "Quadro Emergenza",
        "Sala Macchine", "Centrale Termica", "UTA Piano 1", "UTA Piano 2", "UTA Piano 3",
        "Cabina Elettrica", "Locale Tecnico", "Server Room", "Reception", "Uffici"
    ]

    ubicazioni_arrivo = [
        "Pompa P1", "Pompa P2", "Ventilatore V1", "Ventilatore V2", "Chiller CH1",
        "Sensore Temp", "Sensore Umidità", "Rilevatore Fumo", "Telecamera", "Access Point",
        "Switch", "Illuminazione LED", "Presa Forza Motrice", "Motore M1", "Valvola V1",
        "Termostato", "Sirena Allarme", "Citofono", "Cancello Automatico", "UPS"
    ]

    # Genera 100 cavi
    cavi = []

    for i in range(1, 101):
        # Genera dati casuali ma realistici
        sistema = random.choice(sistemi)
        utility = random.choice(utilities)

        # Logica per rendere i dati più coerenti
        if sistema == "Power":
            utility = "Power"
            colore = random.choice(["Nero", "Grigio", "Marrone"])
            tipologia = random.choice(["N07V-K", "N1VV-K", "FG16OR16", "H07RN-F"])
        elif sistema == "BMS" or sistema == "HVAC":
            utility = "Control"
            colore = random.choice(["Bianco", "Grigio", "Blu"])
            tipologia = random.choice(["LIYCY", "LIYY", "FROR"])
        elif sistema == "Data":
            utility = "Data"
            colore = random.choice(["Blu", "Grigio"])
            tipologia = random.choice(["UTP Cat6", "FTP Cat6A"])
        else:
            colore = random.choice(colori_cavo)
            tipologia = random.choice(tipologie)

        # Sezione coerente con tipologia
        if tipologia in ["UTP Cat6", "FTP Cat6A"]:
            sezione = "4x2x0.5"
        elif tipologia in ["RG58", "RG6"]:
            sezione = "Coax 75Ω"
        else:
            sezione = random.choice(sezioni)

        # SH (schermatura)
        sh = "S" if tipologia in ["LIYCY", "FTP Cat6A"] or "SH" in sezione else "N"

        # Ubicazioni
        partenza = random.choice(ubicazioni_partenza)
        arrivo = random.choice(ubicazioni_arrivo)

        # Genera codici utenza realistici
        utenza_partenza = f"{partenza.replace(' ', '').upper()[:2]}-{random.randint(1, 99):02d}"
        utenza_arrivo = f"{arrivo.replace(' ', '').upper()[:2]}-{random.randint(1, 99):02d}"

        # Descrizioni
        desc_partenza = f"{partenza} - {sistema}"
        desc_arrivo = f"{arrivo} - {sistema}"

        # Metri teorici realistici
        metri_teorici = round(random.uniform(5.0, 150.0), 1)

        cavo = {
            "id_cavo": f"C{i:03d}",
            "sistema": sistema,
            "utility": utility,
            "colore_cavo": colore,
            "tipologia": tipologia,
            "n_conduttori": str(random.choice([0, 2, 3, 4, 5, 7, 12, 19])),
            "sezione": sezione,
            "sh": sh,
            "ubicazione_partenza": partenza,
            "utenza_partenza": utenza_partenza,
            "descrizione_utenza_partenza": desc_partenza,
            "ubicazione_arrivo": arrivo,
            "utenza_arrivo": utenza_arrivo,
            "descrizione_utenza_arrivo": desc_arrivo,
            "metri_teorici": metri_teorici
        }

        cavi.append(cavo)

    # Crea DataFrame
    df = pd.DataFrame(cavi)

    # Nome file con timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_file = f"test_100_cavi_{timestamp}.xlsx"

    # Salva in Excel con formattazione
    with pd.ExcelWriter(nome_file, engine='openpyxl') as writer:
        # Scrivi i dati
        df.to_excel(writer, sheet_name='Cavi Test', index=False)

        # Ottieni il workbook e worksheet per la formattazione
        workbook = writer.book
        worksheet = writer.sheets['Cavi Test']

        # Formattazione intestazioni
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # Applica formattazione alle intestazioni
        for col_num, column_title in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Imposta larghezza colonne
        column_widths = {
            'A': 12,  # id_cavo
            'B': 15,  # sistema
            'C': 12,  # utility
            'D': 15,  # colore_cavo
            'E': 18,  # tipologia
            'F': 12,  # n_conduttori
            'G': 15,  # sezione
            'H': 8,   # sh
            'I': 20,  # ubicazione_partenza
            'J': 15,  # utenza_partenza
            'K': 25,  # descrizione_utenza_partenza
            'L': 20,  # ubicazione_arrivo
            'M': 15,  # utenza_arrivo
            'N': 25,  # descrizione_utenza_arrivo
            'O': 12   # metri_teorici
        }

        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width

        # Aggiungi bordi
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row in worksheet.iter_rows(min_row=1, max_row=len(df)+1, min_col=1, max_col=len(df.columns)):
            for cell in row:
                cell.border = thin_border

        # Aggiungi foglio con statistiche
        stats_data = {
            'Statistica': [
                'Totale Cavi',
                'Sistemi Diversi',
                'Utilities Diverse',
                'Tipologie Diverse',
                'Metri Totali Teorici',
                'Media Metri per Cavo',
                'Cavi con Schermatura',
                'Cavi Power',
                'Cavi Control',
                'Cavi Data'
            ],
            'Valore': [
                len(df),
                df['sistema'].nunique(),
                df['utility'].nunique(),
                df['tipologia'].nunique(),
                round(df['metri_teorici'].sum(), 1),
                round(df['metri_teorici'].mean(), 1),
                len(df[df['sh'] == 'S']),
                len(df[df['utility'] == 'Power']),
                len(df[df['utility'] == 'Control']),
                len(df[df['utility'] == 'Data'])
            ]
        }

        df_stats = pd.DataFrame(stats_data)
        df_stats.to_excel(writer, sheet_name='Statistiche', index=False)

        # Formatta anche il foglio statistiche
        stats_worksheet = writer.sheets['Statistiche']
        for col_num in range(1, 3):
            cell = stats_worksheet.cell(row=1, column=col_num)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        stats_worksheet.column_dimensions['A'].width = 25
        stats_worksheet.column_dimensions['B'].width = 15

    print(f"✅ File di test creato: {nome_file}")
    print(f"📊 Contiene {len(df)} cavi di test")
    print(f"📁 Dimensione file: {os.path.getsize(nome_file)} bytes")
    print(f"🎯 Sistemi inclusi: {', '.join(df['sistema'].unique())}")
    print(f"⚡ Utilities incluse: {', '.join(df['utility'].unique())}")
    print(f"📏 Metri totali teorici: {df['metri_teorici'].sum():.1f}m")

    return nome_file

def genera_test_parco_bobine():
    """
    Genera un file Excel di test con bobine realistiche per il sistema.
    """

    utilities = ["Power", "Control", "Data", "Signal", "Emergency", "Lighting"]

    tipologie = [
        "TAAGSHBHLV01D", "TACHCHBHLU01I", "TBAACHBHLE04I", "FG16OR16",
        "FROR", "N07V-K", "H07RN-F", "LIYCY", "LIYY", "UTP Cat6",
        "FTP Cat6A", "RG58", "RG6", "FG7OR", "FG7R", "N1VV-K"
    ]

    sezioni = [
        "1X240MM2", "1x185+SH", "3X2,5+YG arm", "4x1.5+SH", "2x0.5+SH",
        "1x1.5", "1x2.5", "1x4", "1x6", "1x10", "1x16", "1x25", "1x35",
        "3x2.5+2.5YG", "4x0.75+SH", "5x1.5", "7x1.5", "12x1.5", "19x1.5"
    ]

    ubicazioni = [
        "Magazzino A", "Magazzino B", "Magazzino C", "Deposito Principale",
        "Area Stoccaggio 1", "Area Stoccaggio 2", "Cantiere Esterno",
        "Locale Bobine", "Deposito Temporaneo", "Container 1", "Container 2"
    ]

    fornitori = ["Prysmian", "Nexans", "General Cable", "Draka", "Cavel", "Brugg"]

    # Genera bobine
    bobine = []

    for i in range(1, 31):  # 30 bobine
        utility = random.choice(utilities)
        tipologia = random.choice(tipologie)

        # Sezione coerente con tipologia
        if tipologia in ["UTP Cat6", "FTP Cat6A"]:
            sezione = "4x2x0.5"
        elif tipologia in ["RG58", "RG6"]:
            sezione = "Coax 75Ω"
        else:
            sezione = random.choice(sezioni)

        # Metri totali realistici per bobine
        metri_totali = random.choice([100, 200, 300, 500, 1000, 1500, 2000])

        # Data DDT realistica
        giorno = random.randint(1, 28)
        mese = random.randint(1, 12)
        anno = random.choice([2024, 2025])
        data_ddt = f"{giorno:02d}/{mese:02d}/{anno}"

        bobina = {
            "id_bobina": f"BOB{i:03d}" if i % 3 == 0 else "",  # Alcuni con ID personalizzato
            "utility": utility,
            "tipologia": tipologia,
            "n_conduttori": str(random.choice([0, 2, 3, 4, 5, 7, 12, 19])),
            "sezione": sezione,
            "metri_totali": metri_totali,
            "ubicazione_bobina": random.choice(ubicazioni),
            "fornitore": random.choice(fornitori),
            "n_ddt": f"DDT{random.randint(100000, 999999)}",
            "data_ddt": data_ddt
        }

        bobine.append(bobina)

    # Crea DataFrame
    df = pd.DataFrame(bobine)

    # Nome file con timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_file = f"test_parco_bobine_{timestamp}.xlsx"

    # Salva in Excel
    df.to_excel(nome_file, index=False, sheet_name='Parco Bobine Test')

    print(f"✅ File parco bobine creato: {nome_file}")
    print(f"📊 Contiene {len(df)} bobine di test")
    print(f"📁 Dimensione file: {os.path.getsize(nome_file)} bytes")
    print(f"📏 Metri totali: {df['metri_totali'].sum():,}m")

    return nome_file

if __name__ == "__main__":
    print("=== GENERAZIONE FILE DI TEST ===\n")

    print("1. Generazione file cavi...")
    file_cavi = genera_test_100_cavi()

    print("\n2. Generazione file parco bobine...")
    file_bobine = genera_test_parco_bobine()

    print(f"\n=== FILE GENERATI ===")
    print(f"📄 Cavi: {file_cavi}")
    print(f"📄 Bobine: {file_bobine}")
    print(f"\n🎯 Puoi usare questi file per testare l'importazione nel sistema!")
