#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os

def verifica_file_test():
    """Verifica che i file di test abbiano le colonne corrette."""
    
    # Trova l'ultimo file di test creato
    files = [f for f in os.listdir('.') if f.startswith('test_cavi_5_')]
    if not files:
        print("❌ Nessun file di test trovato")
        return
    
    latest_file = sorted(files)[-1]
    print(f"📄 Verifico file: {latest_file}")
    
    df = pd.read_excel(latest_file)
    print('\nColonne nel file generato:')
    for col in df.columns:
        print(f'  - {col}')
    
    print('\nVerifica:')
    print(f'✅ Contiene "formazione": {"formazione" in df.columns}')
    print(f'❌ Contiene "sezione": {"sezione" in df.columns}')
    print(f'❌ Contiene "n_conduttori": {"n_conduttori" in df.columns}')
    print(f'❌ Contiene "sh": {"sh" in df.columns}')
    
    if len(df) > 0:
        print(f'\n📊 Esempio formazione: {df.iloc[0]["formazione"]}')
    
    # Pulizia
    try:
        os.remove(latest_file)
        print(f'\n🧹 File rimosso: {latest_file}')
    except:
        pass

if __name__ == "__main__":
    verifica_file_test()
