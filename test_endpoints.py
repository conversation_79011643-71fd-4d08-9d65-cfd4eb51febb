#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_template_endpoints():
    """Test degli endpoint per la creazione dei template."""
    
    print('=== TEST ENDPOINT TEMPLATE CAVI ===')
    try:
        response = requests.get('http://localhost:8001/api/excel/template-cavi')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'Success: {data.get("success", False)}')
            print(f'File URL presente: {"file_url" in data}')
            if "file_url" in data:
                print(f'File URL: {data["file_url"]}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Errore: {e}')

    print('\n=== TEST ENDPOINT TEMPLATE PARCO BOBINE ===')
    try:
        response = requests.get('http://localhost:8001/api/excel/template-parco-bobine')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'Success: {data.get("success", False)}')
            print(f'File URL presente: {"file_url" in data}')
            if "file_url" in data:
                print(f'File URL: {data["file_url"]}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Errore: {e}')

if __name__ == "__main__":
    test_template_endpoints()
