#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import random
from datetime import datetime
import os
import sys

def genera_cavi_test(numero_cavi=100):
    """Genera file Excel di test con cavi."""

    # Dati realistici
    sistemi = ["HVAC", "BMS", "Lighting", "Power", "Security", "Fire Safety", "Data"]
    utilities = ["Power", "Control", "Data", "Signal", "Emergency", "Lighting"]
    colori = ["Nero", "Bianco", "Rosso", "Blu", "Verde", "Giallo", "Grigio"]
    tipologie = ["FG16OR16", "FROR", "N07V-K", "LIYCY", "UTP Cat6", "TAAGSHBHLV01D"]
    formazioni = ["1x2.5", "3x2.5", "4x1.5", "3x2.5+2.5YG", "1X240MM2", "4x1.5+SH"]

    cavi = []
    for i in range(1, numero_cavi + 1):
        cavo = {
            "id_cavo": f"C{i:03d}",
            "sistema": random.choice(sistemi),
            "utility": random.choice(utilities),
            "colore_cavo": random.choice(colori),
            "tipologia": random.choice(tipologie),
            "formazione": random.choice(formazioni),
            "ubicazione_partenza": f"Quadro {random.choice(['Principale', 'Secondario', 'Controllo'])}",
            "utenza_partenza": f"QP-{random.randint(1, 99):02d}",
            "descrizione_utenza_partenza": f"Quadro piano {random.randint(1, 3)}",
            "ubicazione_arrivo": f"{random.choice(['Pompa', 'Ventilatore', 'Sensore'])} {random.choice(['P1', 'V1', 'T1'])}",
            "utenza_arrivo": f"UT-{random.randint(1, 99):02d}",
            "descrizione_utenza_arrivo": f"Utenza piano {random.randint(1, 3)}",
            "metri_teorici": round(random.uniform(10.0, 100.0), 1)
        }
        cavi.append(cavo)

    # Salva file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_file = f"test_cavi_{numero_cavi}_{timestamp}.xlsx"

    df = pd.DataFrame(cavi)
    df.to_excel(nome_file, index=False, sheet_name='Test Cavi')

    return nome_file, len(cavi)

def genera_bobine_test(numero_bobine=30):
    """Genera file Excel di test con bobine."""

    utilities = ["Power", "Control", "Data", "Signal"]
    tipologie = ["TAAGSHBHLV01D", "FROR", "N07V-K", "LIYCY", "UTP Cat6"]
    formazioni = ["1X240MM2", "3x2.5+2.5YG", "4x1.5+SH", "1x2.5", "Coax 75Ω"]
    ubicazioni = ["Magazzino A", "Magazzino B", "Deposito", "Container 1"]
    fornitori = ["Prysmian", "Nexans", "General Cable", "Draka"]

    bobine = []
    for i in range(1, numero_bobine + 1):
        bobina = {
            "id_bobina": f"BOB{i:03d}" if i % 4 == 0 else "",  # Alcuni vuoti per test auto-generazione
            "utility": random.choice(utilities),
            "tipologia": random.choice(tipologie),
            "formazione": random.choice(formazioni),
            "metri_totali": random.choice([500, 1000, 1500, 2000]),
            "ubicazione_bobina": random.choice(ubicazioni),
            "fornitore": random.choice(fornitori),
            "n_ddt": f"DDT{random.randint(100000, 999999)}",
            "data_ddt": f"{random.randint(1, 28):02d}/{random.randint(1, 12):02d}/2025"
        }
        bobine.append(bobina)

    # Salva file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_file = f"test_bobine_{numero_bobine}_{timestamp}.xlsx"

    df = pd.DataFrame(bobine)
    df.to_excel(nome_file, index=False, sheet_name='Test Bobine')

    return nome_file, len(bobine)

def main():
    """Funzione principale con menu interattivo."""

    print("🔧 GENERATORE FILE DI TEST PER CMS")
    print("=" * 40)

    while True:
        print("\nScegli cosa generare:")
        print("1. File cavi (default: 100 cavi)")
        print("2. File parco bobine (default: 30 bobine)")
        print("3. Entrambi i file")
        print("4. Personalizza numero elementi")
        print("0. Esci")

        scelta = input("\nInserisci la tua scelta (0-4): ").strip()

        if scelta == "0":
            print("👋 Arrivederci!")
            break

        elif scelta == "1":
            print("\n📄 Generazione file cavi...")
            nome_file, count = genera_cavi_test(100)
            print(f"✅ Creato: {nome_file} ({count} cavi)")

        elif scelta == "2":
            print("\n📦 Generazione file bobine...")
            nome_file, count = genera_bobine_test(30)
            print(f"✅ Creato: {nome_file} ({count} bobine)")

        elif scelta == "3":
            print("\n📄 Generazione file cavi...")
            file_cavi, count_cavi = genera_cavi_test(100)
            print(f"✅ Creato: {file_cavi} ({count_cavi} cavi)")

            print("\n📦 Generazione file bobine...")
            file_bobine, count_bobine = genera_bobine_test(30)
            print(f"✅ Creato: {file_bobine} ({count_bobine} bobine)")

        elif scelta == "4":
            try:
                print("\n🔢 Personalizzazione...")
                tipo = input("Tipo (cavi/bobine): ").strip().lower()
                numero = int(input("Numero elementi: "))

                if tipo.startswith("c"):
                    nome_file, count = genera_cavi_test(numero)
                    print(f"✅ Creato: {nome_file} ({count} cavi)")
                elif tipo.startswith("b"):
                    nome_file, count = genera_bobine_test(numero)
                    print(f"✅ Creato: {nome_file} ({count} bobine)")
                else:
                    print("❌ Tipo non valido! Usa 'cavi' o 'bobine'")

            except ValueError:
                print("❌ Numero non valido!")

        else:
            print("❌ Scelta non valida!")

        input("\nPremi INVIO per continuare...")

if __name__ == "__main__":
    # Se chiamato con argomenti, genera direttamente
    if len(sys.argv) > 1:
        if sys.argv[1] == "cavi":
            numero = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            nome_file, count = genera_cavi_test(numero)
            print(f"✅ File cavi creato: {nome_file} ({count} elementi)")
        elif sys.argv[1] == "bobine":
            numero = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            nome_file, count = genera_bobine_test(numero)
            print(f"✅ File bobine creato: {nome_file} ({count} elementi)")
        else:
            print("❌ Uso: python genera_file_test.py [cavi|bobine] [numero]")
    else:
        # Altrimenti mostra il menu interattivo
        main()
