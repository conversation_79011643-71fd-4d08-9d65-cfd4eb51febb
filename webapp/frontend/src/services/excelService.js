import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const excelService = {
  // Importa cavi da Excel
  importCavi: async (cantiereId, formData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Modifica la configurazione per l'upload di file
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      };

      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);
      return response.data;
    } catch (error) {
      console.error('Import cavi error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Importa parco bobine da Excel
  importParcoBobine: async (cantiereId, formData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Modifica la configurazione per l'upload di file
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      };

      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);
      return response.data;
    } catch (error) {
      console.error('Import parco bobine error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea template Excel per cavi
  createCaviTemplate: async () => {
    try {
      const response = await axiosInstance.get('/excel/template-cavi');

      // Se la risposta contiene un file_url, avvia automaticamente il download
      if (response.data && response.data.file_url) {
        await excelService.downloadFile(response.data.file_url, 'template_cavi.xlsx');
      }

      return response.data;
    } catch (error) {
      console.error('Create cavi template error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea template Excel per parco bobine
  createParcoBobineTemplate: async () => {
    try {
      const response = await axiosInstance.get('/excel/template-parco-bobine');

      // Se la risposta contiene un file_url, avvia automaticamente il download
      if (response.data && response.data.file_url) {
        await excelService.downloadFile(response.data.file_url, 'template_parco_bobine.xlsx');
      }

      return response.data;
    } catch (error) {
      console.error('Create parco bobine template error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Funzione helper per scaricare automaticamente un file
  downloadFile: async (url, filename) => {
    try {
      // Metodo 1: Prova con fetch e blob (migliore per CORS)
      try {
        const response = await fetch(url);
        if (response.ok) {
          const blob = await response.blob();
          const blobUrl = window.URL.createObjectURL(blob);

          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = filename;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Pulisci l'URL del blob
          window.URL.revokeObjectURL(blobUrl);

          console.log(`Download completato per: ${filename}`);
          return true;
        }
      } catch (fetchError) {
        console.warn('Fetch download fallito, provo con link diretto:', fetchError);
      }

      // Metodo 2: Fallback con link diretto
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Download avviato per: ${filename}`);
      return true;
    } catch (error) {
      console.error('Errore durante il download automatico:', error);
      return false;
    }
  },

  // Esporta cavi in Excel
  exportCavi: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`);

      // Se la risposta contiene un file_url, avvia automaticamente il download
      if (response.data && response.data.file_url) {
        await excelService.downloadFile(response.data.file_url, `export_cavi_cantiere_${cantiereIdNum}.xlsx`);
      }

      return response.data;
    } catch (error) {
      console.error('Export cavi error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Esporta parco bobine in Excel
  exportParcoBobine: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`);

      // Se la risposta contiene un file_url, avvia automaticamente il download
      if (response.data && response.data.file_url) {
        await excelService.downloadFile(response.data.file_url, `export_parco_bobine_cantiere_${cantiereIdNum}.xlsx`);
      }

      return response.data;
    } catch (error) {
      console.error('Export parco bobine error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default excelService;
