#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).resolve().parent))

from modules.excel_manager import crea_template_excel, crea_template_parco_bobine

def test_template_creation():
    """Test della creazione dei template Excel."""
    
    print('=== TEST CREAZIONE TEMPLATE CAVI ===')
    try:
        # Test con percorso specifico
        test_path = "test_template_cavi.xlsx"
        result = crea_template_excel(test_path)
        
        if result and os.path.exists(result):
            print(f'✅ Template cavi creato: {result}')
            print(f'   Dimensione file: {os.path.getsize(result)} bytes')
            
            # Verifica che sia un file Excel valido
            try:
                import pandas as pd
                df = pd.read_excel(result, skiprows=1)  # Salta la riga di istruzioni
                print(f'   Colonne: {list(df.columns)}')
                print(f'   Righe di esempio: {len(df)}')
            except Exception as e:
                print(f'   ⚠️ Errore nella lettura del file: {e}')
        else:
            print('❌ Errore nella creazione del template cavi')
            
    except Exception as e:
        print(f'❌ Errore: {e}')
        import traceback
        traceback.print_exc()

    print('\n=== TEST CREAZIONE TEMPLATE PARCO BOBINE ===')
    try:
        # Test con percorso specifico
        test_path = "test_template_parco_bobine.xlsx"
        result = crea_template_parco_bobine(test_path)
        
        if result and os.path.exists(result):
            print(f'✅ Template parco bobine creato: {result}')
            print(f'   Dimensione file: {os.path.getsize(result)} bytes')
            
            # Verifica che sia un file Excel valido
            try:
                import pandas as pd
                df = pd.read_excel(result, skiprows=1)  # Salta la riga di istruzioni
                print(f'   Colonne: {list(df.columns)}')
                print(f'   Righe di esempio: {len(df)}')
            except Exception as e:
                print(f'   ⚠️ Errore nella lettura del file: {e}')
        else:
            print('❌ Errore nella creazione del template parco bobine')
            
    except Exception as e:
        print(f'❌ Errore: {e}')
        import traceback
        traceback.print_exc()

    # Pulizia file di test
    for test_file in ["test_template_cavi.xlsx", "test_template_parco_bobine.xlsx"]:
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f'\n🧹 File di test rimosso: {test_file}')
        except:
            pass

if __name__ == "__main__":
    test_template_creation()
